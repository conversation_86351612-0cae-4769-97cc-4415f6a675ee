import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Settings:
    # Database settings
    DB_IP: str = os.getenv('DB_IP', 'localhost')
    DB_USER: str = os.getenv('DB_USER', '')
    DB_PASSWORD: str = os.getenv('DB_PASSWORD', '')
    DB_NAME: str = os.getenv('DB_NAME', 'IBDB_DEV')
    
    # Cache settings - use data directory as base with hierarchical structure
    CACHE_DIR: Path = Path("data")
    RAW_DATA_FILE: str = "raw/raw_data.parquet"
    NDX_DATA_FILE: str = "raw/ndx_data.parquet"
    VIX_DATA_FILE: str = "raw/vix_data.parquet"
    EXECUTED_TRADES_FILE: str = "raw/executed_trades.parquet"
    TRADE_REPORTS_FILE: str = "raw/trade_reports.parquet"
    RESAMPLED_DATA_FILE: str = "processed/resampled_30s_data.parquet"
    NDX_RESAMPLED_DATA_FILE: str = "processed/ndx_resampled_30s_data.parquet"
    BUCKET_VERSION_FILE: str = "metadata/bucket_version.parquet"
    BUCKET_SPEC_FILE: str = "metadata/bucket_spec.parquet"
    BACKTEST_LATEST_SAVED_FILE: str = "raw/backtest_latest_saved.parquet"

    BUCKET_RESAMPLED_DIR: str = "processed/bucket_resampled"
    CACHE_STATUS_FILE: str = "metadata/cache_status.json"
    
    # API settings
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "JerryVision"
    API_BASE_URL: str = os.getenv('API_BASE_URL', 'http://localhost:8000')
    
    # Data processing settings
    RESAMPLE_FREQUENCY: str = "30s"  # 30 seconds (default)
    
    # Market hours settings
    MARKET_OPEN_TIME: str = "09:30"  # 9:30 AM
    MARKET_CLOSE_TIME: str = "16:00"  # 4:00 PM
    
    # Default time range settings for data viewing
    DEFAULT_START_TIME: str = "15:49:00"  # 3:49 PM
    DEFAULT_END_TIME: str = "16:00:00"    # 4:00 PM
    
    def __init__(self):
        # Ensure cache directory exists
        self.CACHE_DIR.mkdir(parents=True, exist_ok=True)
        # Ensure subdirectories exist
        (self.CACHE_DIR / "raw").mkdir(parents=True, exist_ok=True)
        (self.CACHE_DIR / "processed").mkdir(parents=True, exist_ok=True)
        (self.CACHE_DIR / "processed/bucket_resampled").mkdir(parents=True, exist_ok=True)
        (self.CACHE_DIR / "metadata").mkdir(parents=True, exist_ok=True)
        (self.CACHE_DIR / "cache").mkdir(parents=True, exist_ok=True)

settings = Settings() 